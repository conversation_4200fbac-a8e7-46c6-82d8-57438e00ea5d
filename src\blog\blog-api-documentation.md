# Blog API Documentation

This document provides examples of how to use the Blog API endpoints correctly.

## Base URL

All API endpoints are prefixed with:
```
http://localhost:3300/api/blog
```

## Blog Management

### Create a New Blog Post

Creates a new blog post with content blocks, author information, and SEO data.

**Endpoint:**
```
POST /api/blog
```

**Request Body:**
```json
{
  "title": "The Ultimate Guide to Organic Skincare",
  "slug": "ultimate-guide-organic-skincare",
  "tags": ["skincare", "organic", "beauty", "natural"],
  "content": [
    {
      "type": "text",
      "content": "In today's world of endless beauty products, finding the right skincare routine can feel overwhelming."
    },
    {
      "type": "image",
      "content": "",
      "imageUrl": "https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=800&auto=format&fit=crop",
      "imageAlt": "Organic skincare products arranged beautifully"
    },
    {
      "type": "heading",
      "content": "Why Choose Organic Skincare?",
      "level": 2
    },
    {
      "type": "list",
      "content": "Benefits of organic skincare:",
      "listItems": [
        "Reduces exposure to harmful chemicals",
        "Gentler on sensitive skin",
        "Environmentally sustainable"
      ]
    }
  ],
  "blogImage": "https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=1200&auto=format&fit=crop",
  "author": {
    "name": "Sarah Johnson",
    "avatar": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&auto=format&fit=crop",
    "bio": "Certified organic skincare specialist with 10+ years of experience."
  },
  "excerpt": "Discover the transformative power of organic skincare and learn how to build a natural beauty routine.",
  "readTime": 8,
  "featured": true,
  "published": true,
  "seo": {
    "metaTitle": "Ultimate Guide to Organic Skincare - Natural Beauty Tips",
    "metaDescription": "Learn how to transform your beauty routine with organic skincare.",
    "keywords": ["organic skincare", "natural beauty", "skincare routine"]
  }
}
```

### Get All Blog Posts

Retrieves a paginated list of blog posts with optional filtering.

**Endpoint:**
```
GET /api/blog?page=1&limit=10&published=true&featured=false&tags=skincare,beauty
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of posts per page (default: 10)
- `published` (optional): Filter by published status (true/false)
- `featured` (optional): Filter by featured status (true/false)
- `tags` (optional): Comma-separated list of tags to filter by

**Example Response:**
```json
{
  "data": [
    {
      "id": 1,
      "title": "The Ultimate Guide to Organic Skincare",
      "slug": "ultimate-guide-organic-skincare",
      "tags": ["skincare", "organic", "beauty"],
      "blogImage": "https://images.unsplash.com/photo-1556228578-8c89e6adf883",
      "author": {
        "name": "Sarah Johnson",
        "avatar": "https://images.unsplash.com/photo-1494790108755-2616b612b786",
        "bio": "Certified organic skincare specialist"
      },
      "createdDate": "2024-01-15T10:00:00Z",
      "excerpt": "Discover the transformative power of organic skincare...",
      "readTime": 8,
      "featured": true,
      "published": true,
      "createdAt": "2024-01-15T10:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10,
  "totalPages": 1
}
```

### Get Blog Post by ID

**Endpoint:**
```
GET /api/blog/:id
```

### Get Blog Post by Slug

**Endpoint:**
```
GET /api/blog/slug/:slug
```

### Get Featured Blog Posts

**Endpoint:**
```
GET /api/blog/featured
```

### Get All Tags

**Endpoint:**
```
GET /api/blog/tags
```

### Update Blog Post

**Endpoint:**
```
PATCH /api/blog/:id
```

### Delete Blog Post

**Endpoint:**
```
DELETE /api/blog/:id
```

## Content Block Types

The blog content supports various block types:

1. **Text Block:**
```json
{
  "type": "text",
  "content": "Your text content here"
}
```

2. **Image Block:**
```json
{
  "type": "image",
  "content": "",
  "imageUrl": "https://example.com/image.jpg",
  "imageAlt": "Image description"
}
```

3. **Heading Block:**
```json
{
  "type": "heading",
  "content": "Your heading text",
  "level": 2
}
```

4. **List Block:**
```json
{
  "type": "list",
  "content": "List title",
  "listItems": ["Item 1", "Item 2", "Item 3"]
}
```

5. **Quote Block:**
```json
{
  "type": "quote",
  "content": "Your quote text here"
}
```
