export class BlogContentBlockResponseDto {
  type: string;
  content: string;
  imageUrl?: string;
  imageAlt?: string;
  level?: number;
  listItems?: string[];
}

export class BlogAuthorResponseDto {
  name: string;
  avatar?: string;
  bio?: string;
}

export class BlogSeoResponseDto {
  metaTitle?: string;
  metaDescription?: string;
  keywords?: string[];
}

export class BlogResponseDto {
  id: number;
  title: string;
  slug: string;
  tags: string[];
  content: BlogContentBlockResponseDto[];
  blogImage?: string;
  author?: BlogAuthorResponseDto;
  createdDate: Date;
  excerpt?: string;
  readTime?: number;
  featured: boolean;
  published: boolean;
  seo?: BlogSeoResponseDto;
  createdAt: Date;
  updatedAt: Date;
}

export class BlogListResponseDto {
  id: number;
  title: string;
  slug: string;
  tags: string[];
  blogImage?: string;
  author?: BlogAuthorResponseDto;
  createdDate: Date;
  excerpt?: string;
  readTime?: number;
  featured: boolean;
  published: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class PaginatedBlogsResponseDto {
  data: BlogListResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
