import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAttributeDto } from './dto/create-attribute.dto';
import { Attribute, Product, ProductType } from '@prisma/client';
import { CreateGroupedProductDto } from './dto/create-grouped-product.dto';
import { UpdateGroupedProductDto } from './dto/update-grouped-product.dto';
import { UpdateVariantDto } from './dto/update-variant.dto';

@Injectable()
export class GroupedProductsService {
  constructor(private prisma: PrismaService) {}

  async createAttribute(createAttributeDto: CreateAttributeDto): Promise<Attribute> {
    const { ...attributeData } = createAttributeDto;

    try {
      const attribute = await this.prisma.attribute.create({
        data: attributeData
      });

      return attribute;
    } catch (error) {
      console.error('Error creating attribute:', error);
      throw error;
    }
  }

  async getAllAttributes(): Promise<Attribute[]> {
    return this.prisma.attribute.findMany({
      select: {
        id: true,
        name: true
      }
    });
  }

  async getAttributeById(id: number): Promise<Attribute> {
    const attribute = await this.prisma.attribute.findUnique({
      where: { id },
      select: {
        id: true,
        name: true
      }
    });

    if (!attribute) {
      throw new NotFoundException(`Attribute with ID ${id} not found`);
    }

    return attribute;
  }

  async getGroupedProductById(id: number): Promise<Product> {
    const product = await this.prisma.product.findUnique({
      where: { id },
      include: {
        images: true,
        categories: true,
        tags: true,
        listings: true,
        ProductAttribute: {
          include: {
            attribute: true,
            values: true,
          },
        },
        variants: {
          include: {
            ProductImage: true,
            attributes: {
              include: {
                value: true,
              },
            },
          },
        },
      },
    });

    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }

    if (product.type !== ProductType.GROUPED) {
      throw new BadRequestException(`Product with ID ${id} is not a grouped product`);
    }

    return product;
  }

  async createGroupedProduct(createProductDto: CreateGroupedProductDto): Promise<Product> {
    try {
      // Ensure the product type is GROUPED
      const productData = {
        ...createProductDto,
        type: ProductType.GROUPED
      };

      const {
        categoryIds = [],
        images = [],
        tags = [],
        listings = [],
        productAttributes = [],
        variants = [],
        ...baseProductData
      } = productData;

      // Map to store client-side placeholder IDs → real DB IDs
      const placeholderToDbValueId = {};

      // 1) Create the base product
      const product = await this.prisma.product.create({
        data: {
          ...baseProductData,
          // Generate a slug from the name to ensure uniqueness
          slug: baseProductData.name.toLowerCase().replace(/\s+/g, '-'),
          ...(images.length > 0
            ? {
                images: {
                  create: images,
                },
              }
            : {}),
          ...(categoryIds.length > 0
            ? {
                categories: {
                  connect: categoryIds.map(id => ({ id })),
                },
              }
            : {}),
          ...(tags.length > 0
            ? {
                tags: {
                  connectOrCreate: tags.map(tag => ({
                    where: { name: tag },
                    create: {
                      name: tag,
                      slug: tag.toLowerCase().replace(/\s+/g, '-')
                    }
                  }))
                },
              }
            : {}),
        },
      });

      // 2) Create category relationships with skipDuplicates
      if (categoryIds.length > 0) {
        await this.prisma.productCategories.createMany({
          data: categoryIds.map((categoryId) => ({
            productId: product.id,
            categoryId,
          })),
          skipDuplicates: true,
        });
      }

      // 3) Create tag relationships if needed
      if (tags.length > 0) {
        // Create ProductTags entries
        for (const tagName of tags) {
          // Find or create the tag
          const tag = await this.prisma.tag.upsert({
            where: { name: tagName },
            update: {},
            create: {
              name: tagName,
              slug: tagName.toLowerCase().replace(/\s+/g, '-')
            },
          });

          // Create the relationship
          await this.prisma.productTags.create({
            data: {
              productId: product.id,
              tagId: tag.id,
            },
          });
        }
      }

      // 4) Create listings if provided
      if (listings.length > 0) {
        await this.prisma.listing.createMany({
          data: listings.map(listing => ({
            title: listing.title,
            content: listing.content,
            productId: product.id
          })),
        });
      }

      // 5) Create product attributes and their values
      if (productAttributes.length > 0) {
        for (const attributeData of productAttributes) {
          const { attributeId, values = [] } = attributeData;

          // Create the product attribute
          const productAttribute = await this.prisma.productAttribute.create({
            data: {
              product: { connect: { id: product.id } },
              attribute: { connect: { id: attributeId } },
            },
          });

          // Create attribute values
          if (values.length > 0) {
            for (const valueData of values) {
              const created = await this.prisma.productAttributeValue.create({
                data: {
                  productAttribute: { connect: { id: productAttribute.id } },
                  value: valueData.value,
                },
              });

              // Map the client placeholder ID → real DB ID
              if (valueData.id) {
                placeholderToDbValueId[valueData.id] = created.id;
              }
            }
          }
        }
      }

      // 6) Create variants
      if (variants.length > 0) {
        for (const variantData of variants) {
          const { attributeValueIds = [], images: variantImages = [], ...variantFields } = variantData;

          // Create the variant
          const variant = await this.prisma.productVariant.create({
            data: {
              ...variantFields,
              product: { connect: { id: product.id } },
              ...(variantImages.length > 0
                ? {
                    ProductImage: {
                      create: variantImages,
                    },
                  }
                : {}),
            },
          });

          // Translate placeholders → real IDs (fall back to original if it already existed)
          const dbValueIds = attributeValueIds.map(id =>
            placeholderToDbValueId[id] ?? id
          );

          // Connect attribute values to variant
          if (dbValueIds.length > 0) {
            await this.prisma.variantAttributeValue.createMany({
              data: dbValueIds.map(valueId => ({
                variantId: variant.id,
                valueId,
              })),
              skipDuplicates: true,
            });
          }
        }
      }

      // 7) Return the complete product with relationships
      return await this.prisma.product.findUnique({
        where: { id: product.id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
          ProductAttribute: {
            include: {
              attribute: true,
              values: true,
            },
          },
          variants: {
            include: {
              ProductImage: true,
              attributes: {
                include: {
                  value: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      console.error('Error creating grouped product:', error);
      throw error;
    }
  }

  async createGroupedProductForCategory(categoryId: number, createProductDto: CreateGroupedProductDto): Promise<Product> {
    try {
      // Check if the category exists
      const category = await this.prisma.category.findUnique({
        where: { id: categoryId },
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${categoryId} not found`);
      }

      // Ensure the product is associated with the specified category
      const productWithCategory = {
        ...createProductDto,
        categoryIds: [...(createProductDto.categoryIds || []), categoryId]
      };

      // Create the grouped product with the category
      return this.createGroupedProduct(productWithCategory);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error creating grouped product for category:', error);
      throw error;
    }
  }

  async updateGroupedProduct(id: number, updateProductDto: UpdateGroupedProductDto): Promise<Product> {
    try {
      // First check if the product exists
      const product = await this.prisma.product.findUnique({
        where: { id },
        include: {
          images: true,
          categories: true,
          listings: true,
          tags: true,
          ProductAttribute: {
            include: {
              attribute: true,
              values: true,
            },
          },
          variants: {
            include: {
              ProductImage: true,
              attributes: {
                include: {
                  value: true,
                },
              },
            },
          },
        },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      if (product.type !== ProductType.GROUPED) {
        throw new BadRequestException(`Product with ID ${id} is not a grouped product`);
      }

      // Extract data from the DTO
      const {
        categoryIds,
        images,
        tags,
        listings,
        productAttributes,
        variants,
        ...baseProductData
      } = updateProductDto;

      // Map to store client-side placeholder IDs → real DB IDs
      const placeholderToDbValueId = {};

      // 1) Update the base product
      await this.prisma.product.update({
        where: { id },
        data: baseProductData,
      });

      // 2) Handle categories if provided
      if (categoryIds) {
        // Remove existing category relationships
        await this.prisma.productCategories.deleteMany({
          where: { productId: id },
        });

        // Create new category relationships
        if (categoryIds.length > 0) {
          await this.prisma.productCategories.createMany({
            data: categoryIds.map((categoryId) => ({
              productId: id,
              categoryId,
            })),
            skipDuplicates: true,
          });
        }
      }

      // 3) Handle tags if provided
      if (tags) {
        // Remove existing tag relationships
        await this.prisma.productTags.deleteMany({
          where: { productId: id },
        });

        // Create new tag relationships
        if (tags.length > 0) {
          for (const tagName of tags) {
            // Find or create the tag
            const tag = await this.prisma.tag.upsert({
              where: { name: tagName },
              update: {},
              create: {
                name: tagName,
                slug: tagName.toLowerCase().replace(/\s+/g, '-')
              },
            });

            // Create the relationship
            await this.prisma.productTags.create({
              data: {
                productId: id,
                tagId: tag.id,
              },
            });
          }
        }
      }

      // 4) Handle images if provided
      if (images) {
        // Process each image
        for (const imageData of images) {
          if (imageData.id) {
            // Existing image
            if (imageData.delete) {
              // Delete the image
              await this.prisma.productImage.delete({
                where: { id: imageData.id },
              });
            } else {
              // Update the image
              await this.prisma.productImage.update({
                where: { id: imageData.id },
                data: {
                  url: imageData.url,
                  position: imageData.position,
                },
              });
            }
          } else {
            // New image
            await this.prisma.productImage.create({
              data: {
                productId: id,
                url: imageData.url,
                position: imageData.position,
              },
            });
          }
        }
      }

      // 5) Handle listings if provided
      if (listings) {
        // Process each listing
        for (const listingData of listings) {
          if (listingData.id) {
            // Existing listing
            if (listingData.delete) {
              // Delete the listing
              await this.prisma.listing.delete({
                where: { id: listingData.id },
              });
            } else {
              // Update the listing
              await this.prisma.listing.update({
                where: { id: listingData.id },
                data: {
                  title: listingData.title,
                  content: listingData.content,
                },
              });
            }
          } else {
            // New listing
            await this.prisma.listing.create({
              data: {
                productId: id,
                title: listingData.title,
                content: listingData.content,
              },
            });
          }
        }
      }

      // 6) Handle product attributes if provided
      if (productAttributes) {
        // Process each attribute
        for (const attributeData of productAttributes) {
          if (attributeData.id) {
            // Existing attribute
            if (attributeData.delete) {
              // Delete attribute values first
              const productAttribute = await this.prisma.productAttribute.findUnique({
                where: { id: attributeData.id },
                include: { values: true },
              });

              if (productAttribute) {
                for (const value of productAttribute.values) {
                  // Delete variant attribute value relationships
                  await this.prisma.variantAttributeValue.deleteMany({
                    where: { valueId: value.id },
                  });

                  // Delete the value
                  await this.prisma.productAttributeValue.delete({
                    where: { id: value.id },
                  });
                }

                // Delete the product attribute
                await this.prisma.productAttribute.delete({
                  where: { id: attributeData.id },
                });
              }
            } else if (attributeData.values) {
              // Handle attribute values
              for (const valueData of attributeData.values) {
                if (valueData.id) {
                  // Existing value
                  if (valueData.delete) {
                    // Delete variant attribute value relationships
                    await this.prisma.variantAttributeValue.deleteMany({
                      where: { valueId: valueData.id },
                    });

                    // Delete the value
                    await this.prisma.productAttributeValue.delete({
                      where: { id: valueData.id },
                    });
                  } else {
                    // Update the value
                    await this.prisma.productAttributeValue.update({
                      where: { id: valueData.id },
                      data: { value: valueData.value },
                    });
                  }
                } else {
                  // New value
                  const created = await this.prisma.productAttributeValue.create({
                    data: {
                      productAttributeId: attributeData.id,
                      value: valueData.value,
                    },
                  });

                  // Map the client placeholder ID → real DB ID
                  if (valueData.id) {
                    placeholderToDbValueId[valueData.id] = created.id;
                  }
                }
              }
            }
          } else {
            // New attribute
            const { attributeId, values = [] } = attributeData;

            // Create the product attribute
            const productAttribute = await this.prisma.productAttribute.create({
              data: {
                productId: id,
                attributeId,
              },
            });

            // Create attribute values
            if (values.length > 0) {
              for (const valueData of values) {
                const created = await this.prisma.productAttributeValue.create({
                  data: {
                    productAttributeId: productAttribute.id,
                    value: valueData.value,
                  },
                });

                // Map the client placeholder ID → real DB ID
                if (valueData.id) {
                  placeholderToDbValueId[valueData.id] = created.id;
                }
              }
            }
          }
        }
      }

      // 7) Handle variants if provided
      if (variants) {
        // Process each variant
        for (const variantData of variants) {
          const { id: variantId, attributeValueIds, images: variantImages, delete: shouldDelete, ...variantFields } = variantData;

          if (variantId) {
            // Existing variant
            if (shouldDelete) {
              // Delete variant attribute values
              await this.prisma.variantAttributeValue.deleteMany({
                where: { variantId },
              });

              // Delete variant images
              await this.prisma.productImage.deleteMany({
                where: { variantId },
              });

              // Delete the variant
              await this.prisma.productVariant.delete({
                where: { id: variantId },
              });
            } else {
              // Update the variant
              await this.prisma.productVariant.update({
                where: { id: variantId },
                data: variantFields,
              });

              // Handle variant images if provided
              if (variantImages) {
                for (const imageData of variantImages) {
                  if (imageData.id) {
                    // Existing image
                    if (imageData.delete) {
                      // Delete the image
                      await this.prisma.productImage.delete({
                        where: { id: imageData.id },
                      });
                    } else {
                      // Update the image
                      await this.prisma.productImage.update({
                        where: { id: imageData.id },
                        data: {
                          url: imageData.url,
                          position: imageData.position,
                        },
                      });
                    }
                  } else {
                    // New image
                    await this.prisma.productImage.create({
                      data: {
                        variantId,
                        url: imageData.url,
                        position: imageData.position,
                      },
                    });
                  }
                }
              }

              // Handle attribute values if provided
              // Only modify attribute relationships when attributeValueIds is explicitly provided (not undefined)
              if (attributeValueIds !== undefined) {
                // Remove existing attribute value relationships
                await this.prisma.variantAttributeValue.deleteMany({
                  where: { variantId },
                });

                // Translate placeholders → real IDs (fall back to original if it already existed)
                const dbValueIds = attributeValueIds.map(id =>
                  placeholderToDbValueId[id] ?? id
                );

                // Create new attribute value relationships
                if (dbValueIds.length > 0) {
                  await this.prisma.variantAttributeValue.createMany({
                    data: dbValueIds.map(valueId => ({
                      variantId,
                      valueId,
                    })),
                    skipDuplicates: true,
                  });
                }
              }
            }
          } else {
            // New variant
            const variant = await this.prisma.productVariant.create({
              data: {
                ...variantFields,
                productId: id,
              },
            });

            // Handle variant images if provided
            if (variantImages && variantImages.length > 0) {
              await this.prisma.productImage.createMany({
                data: variantImages.map(image => ({
                  variantId: variant.id,
                  url: image.url,
                  position: image.position,
                })),
              });
            }

            // Handle attribute values if provided
            if (attributeValueIds && attributeValueIds.length > 0) {
              // Translate placeholders → real IDs (fall back to original if it already existed)
              const dbValueIds = attributeValueIds.map(valueId =>
                placeholderToDbValueId[valueId] ?? valueId
              );

              // Create attribute value relationships
              await this.prisma.variantAttributeValue.createMany({
                data: dbValueIds.map(valueId => ({
                  variantId: variant.id,
                  valueId,
                })),
                skipDuplicates: true,
              });
            }
          }
        }
      }

      // 8) Return the updated product with all relationships
      return await this.prisma.product.findUnique({
        where: { id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
          ProductAttribute: {
            include: {
              attribute: true,
              values: true,
            },
          },
          variants: {
            include: {
              ProductImage: true,
              attributes: {
                include: {
                  value: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      console.error('Error updating grouped product:', error);
      throw error;
    }
  }

  async updateGroupedProductBase(id: number, updateProductDto: UpdateGroupedProductDto): Promise<Product> {
    try {
      // First check if the product exists
      const product = await this.prisma.product.findUnique({
        where: { id },
        include: {
          images: true,
          categories: true,
          listings: true,
          tags: true,
        },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      if (product.type !== ProductType.GROUPED) {
        throw new BadRequestException(`Product with ID ${id} is not a grouped product`);
      }

      // Extract data from the DTO, excluding variant-related fields
      const {
        categoryIds,
        images,
        tags,
        listings,
        variants, // Exclude variants
        productAttributes, // Exclude product attributes
        ...baseProductData
      } = updateProductDto;

      // 1) Update the base product
      await this.prisma.product.update({
        where: { id },
        data: baseProductData,
      });

      // 2) Handle categories if provided
      if (categoryIds) {
        // Remove existing category relationships
        await this.prisma.productCategories.deleteMany({
          where: { productId: id },
        });

        // Create new category relationships
        if (categoryIds.length > 0) {
          await this.prisma.productCategories.createMany({
            data: categoryIds.map((categoryId) => ({
              productId: id,
              categoryId,
            })),
            skipDuplicates: true,
          });
        }
      }

      // 3) Handle tags if provided
      if (tags) {
        // Remove existing tag relationships
        await this.prisma.productTags.deleteMany({
          where: { productId: id },
        });

        // Create new tag relationships
        if (tags.length > 0) {
          for (const tagName of tags) {
            // Find or create the tag
            const tag = await this.prisma.tag.upsert({
              where: { name: tagName },
              update: {},
              create: {
                name: tagName,
                slug: tagName.toLowerCase().replace(/\s+/g, '-')
              },
            });

            // Create the relationship
            await this.prisma.productTags.create({
              data: {
                productId: id,
                tagId: tag.id,
              },
            });
          }
        }
      }

      // 4) Handle images if provided
      if (images) {
        // Process each image
        for (const imageData of images) {
          if (imageData.id) {
            // Existing image
            if (imageData.delete) {
              // Delete the image
              await this.prisma.productImage.delete({
                where: { id: imageData.id },
              });
            } else {
              // Update the image
              await this.prisma.productImage.update({
                where: { id: imageData.id },
                data: {
                  url: imageData.url,
                  position: imageData.position,
                },
              });
            }
          } else {
            // New image
            await this.prisma.productImage.create({
              data: {
                productId: id,
                url: imageData.url,
                position: imageData.position,
              },
            });
          }
        }
      }

      // 5) Handle listings if provided
      if (listings) {
        // Process each listing
        for (const listingData of listings) {
          if (listingData.id) {
            // Existing listing
            if (listingData.delete) {
              // Delete the listing
              await this.prisma.listing.delete({
                where: { id: listingData.id },
              });
            } else {
              // Update the listing
              await this.prisma.listing.update({
                where: { id: listingData.id },
                data: {
                  title: listingData.title,
                  content: listingData.content,
                },
              });
            }
          } else {
            // New listing
            await this.prisma.listing.create({
              data: {
                productId: id,
                title: listingData.title,
                content: listingData.content,
              },
            });
          }
        }
      }

      // 6) Return the updated product with all relationships
      return await this.prisma.product.findUnique({
        where: { id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
          ProductAttribute: {
            include: {
              attribute: true,
              values: true,
            },
          },
          variants: {
            include: {
              ProductImage: true,
              attributes: {
                include: {
                  value: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      console.error('Error updating grouped product base:', error);
      throw error;
    }
  }

  async updateGroupedProductVariant(productId: number, variantId: number, updateVariantDto: UpdateVariantDto): Promise<Product> {
    try {
      // First check if the product exists
      const product = await this.prisma.product.findUnique({
        where: { id: productId },
        include: {
          variants: {
            where: { id: variantId },
            include: {
              ProductImage: true,
              attributes: {
                include: {
                  value: true,
                },
              },
            },
          },
        },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${productId} not found`);
      }

      if (product.type !== ProductType.GROUPED) {
        throw new BadRequestException(`Product with ID ${productId} is not a grouped product`);
      }

      if (!product.variants || product.variants.length === 0) {
        throw new NotFoundException(`Variant with ID ${variantId} not found in product ${productId}`);
      }

      const { images: variantImages, attributeValueIds, ...variantFields } = updateVariantDto;

      // 1) Update the variant
      await this.prisma.productVariant.update({
        where: { id: variantId },
        data: variantFields,
      });

      // 2) Handle variant images if provided
      if (variantImages) {
        for (const imageData of variantImages) {
          if (imageData.id) {
            // Existing image
            if (imageData.delete) {
              // Delete the image
              await this.prisma.productImage.delete({
                where: { id: imageData.id },
              });
            } else {
              // Update the image
              await this.prisma.productImage.update({
                where: { id: imageData.id },
                data: {
                  url: imageData.url,
                  position: imageData.position,
                },
              });
            }
          } else {
            // New image
            await this.prisma.productImage.create({
              data: {
                variantId,
                url: imageData.url,
                position: imageData.position,
              },
            });
          }
        }
      }

      // 3) Handle attribute values if provided
      // Only modify attribute relationships when attributeValueIds is explicitly provided (not undefined)
      // - undefined = don't change existing relationships
      // - [] = remove all relationships
      // - [1, 2, 3] = replace with these specific relationships
      if (attributeValueIds !== undefined) {
        // Remove existing attribute value relationships
        await this.prisma.variantAttributeValue.deleteMany({
          where: { variantId },
        });

        // Create new attribute value relationships
        if (attributeValueIds.length > 0) {
          await this.prisma.variantAttributeValue.createMany({
            data: attributeValueIds.map(valueId => ({
              variantId,
              valueId,
            })),
            skipDuplicates: true,
          });
        }
      }

      // 4) Return the updated product with all relationships
      return await this.prisma.product.findUnique({
        where: { id: productId },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
          ProductAttribute: {
            include: {
              attribute: true,
              values: true,
            },
          },
          variants: {
            include: {
              ProductImage: true,
              attributes: {
                include: {
                  value: true,
                },
              },
            },
          },
        },
      });
    } catch (error) {
      console.error('Error updating grouped product variant:', error);
      throw error;
    }
  }

  async deleteGroupedProduct(id: number): Promise<Product> {
    try {
      // First check if the product exists
      const product = await this.prisma.product.findUnique({
        where: { id },
        include: {
          images: true,
          categories: true,
          listings: true,
          tags: true,
          ProductAttribute: {
            include: {
              values: true,
            },
          },
          variants: true,
        },
      });

      if (!product) {
        throw new NotFoundException(`Product with ID ${id} not found`);
      }

      // For GROUPED products, delete related entities
      if (product.type === 'GROUPED') {
        // Delete variant attribute values
        for (const variant of product.variants) {
          await this.prisma.variantAttributeValue.deleteMany({
            where: { variantId: variant.id },
          });
        }

        // Delete variant images
        await this.prisma.productImage.deleteMany({
          where: {
            variantId: {
              in: product.variants.map(v => v.id)
            }
          },
        });

        // Delete variants
        await this.prisma.productVariant.deleteMany({
          where: { productId: id },
        });

        // Delete attribute values
        for (const attr of product.ProductAttribute) {
          // First delete any variant attribute value relationships
          for (const value of attr.values) {
            await this.prisma.variantAttributeValue.deleteMany({
              where: { valueId: value.id },
            });

            // Then delete the value itself
            await this.prisma.productAttributeValue.delete({
              where: { id: value.id },
            });
          }
        }

        // Delete product attributes
        await this.prisma.productAttribute.deleteMany({
          where: { productId: id },
        });
      }

      // Delete all relations in ProductCategories table
      await this.prisma.productCategories.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all relations in ProductTags table
      await this.prisma.productTags.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all product images
      await this.prisma.productImage.deleteMany({
        where: {
          productId: id,
        },
      });

      // Delete all listings associated with the product
      await this.prisma.listing.deleteMany({
        where: {
          productId: id,
        },
      });

      // Finally delete the product
      return await this.prisma.product.delete({
        where: { id },
        include: {
          images: true,
          categories: true,
          tags: true,
          listings: true,
        },
      });
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error deleting product:', error);
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
  }
}






