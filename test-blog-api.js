// Simple test script to verify blog API functionality
const baseUrl = 'http://localhost:3300/api/blog';

// Sample blog data based on your dummy data structure
const sampleBlog = {
  title: 'The Ultimate Guide to Organic Skincare: Transform Your Beauty Routine',
  slug: 'ultimate-guide-organic-skincare',
  tags: ['skincare', 'organic', 'beauty', 'natural'],
  content: [
    {
      type: 'text',
      content: 'In today\'s world of endless beauty products, finding the right skincare routine can feel overwhelming. However, the secret to radiant, healthy skin might be simpler than you think: going organic.'
    },
    {
      type: 'image',
      content: '',
      imageUrl: 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=800&auto=format&fit=crop',
      imageAlt: 'Organic skincare products arranged beautifully'
    },
    {
      type: 'heading',
      content: 'Why Choose Organic Skincare?',
      level: 2
    },
    {
      type: 'text',
      content: 'Organic skincare products are formulated without harmful chemicals, synthetic fragrances, or artificial preservatives. This means they\'re gentler on your skin and better for the environment.'
    },
    {
      type: 'list',
      content: 'Benefits of organic skincare:',
      listItems: [
        'Reduces exposure to harmful chemicals',
        'Gentler on sensitive skin',
        'Environmentally sustainable',
        'Rich in natural nutrients and antioxidants',
        'Less likely to cause allergic reactions'
      ]
    },
    {
      type: 'quote',
      content: 'Your skin is your largest organ. What you put on it matters just as much as what you put in your body.'
    }
  ],
  blogImage: 'https://images.unsplash.com/photo-**********-8c89e6adf883?w=1200&auto=format&fit=crop',
  author: {
    name: 'Sarah Johnson',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&auto=format&fit=crop',
    bio: 'Certified organic skincare specialist with 10+ years of experience in natural beauty.'
  },
  excerpt: 'Discover the transformative power of organic skincare and learn how to build a natural beauty routine that works for your skin type.',
  readTime: 8,
  featured: true,
  published: true,
  seo: {
    metaTitle: 'Ultimate Guide to Organic Skincare - Natural Beauty Tips',
    metaDescription: 'Learn how to transform your beauty routine with organic skincare. Expert tips, product recommendations, and natural ingredients guide.',
    keywords: ['organic skincare', 'natural beauty', 'skincare routine', 'organic products']
  }
};

async function testBlogAPI() {
  try {
    console.log('🧪 Testing Blog API...\n');

    // Test 1: Create a blog post
    console.log('1. Creating a blog post...');
    const createResponse = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sampleBlog),
    });

    if (createResponse.ok) {
      const createdBlog = await createResponse.json();
      console.log('✅ Blog created successfully:', createdBlog.title);
      console.log('   ID:', createdBlog.id);
      console.log('   Slug:', createdBlog.slug);

      // Test 2: Get blog by ID
      console.log('\n2. Getting blog by ID...');
      const getByIdResponse = await fetch(`${baseUrl}/${createdBlog.id}`);
      if (getByIdResponse.ok) {
        const blogById = await getByIdResponse.json();
        console.log('✅ Blog retrieved by ID:', blogById.title);
      } else {
        console.log('❌ Failed to get blog by ID');
      }

      // Test 3: Get blog by slug
      console.log('\n3. Getting blog by slug...');
      const getBySlugResponse = await fetch(`${baseUrl}/slug/${createdBlog.slug}`);
      if (getBySlugResponse.ok) {
        const blogBySlug = await getBySlugResponse.json();
        console.log('✅ Blog retrieved by slug:', blogBySlug.title);
      } else {
        console.log('❌ Failed to get blog by slug');
      }

      // Test 4: Get all blogs
      console.log('\n4. Getting all blogs...');
      const getAllResponse = await fetch(`${baseUrl}?page=1&limit=10`);
      if (getAllResponse.ok) {
        const allBlogs = await getAllResponse.json();
        console.log('✅ Retrieved all blogs. Total:', allBlogs.total);
        console.log('   Pages:', allBlogs.totalPages);
      } else {
        console.log('❌ Failed to get all blogs');
      }

      // Test 5: Get featured blogs
      console.log('\n5. Getting featured blogs...');
      const getFeaturedResponse = await fetch(`${baseUrl}/featured`);
      if (getFeaturedResponse.ok) {
        const featuredBlogs = await getFeaturedResponse.json();
        console.log('✅ Retrieved featured blogs. Count:', featuredBlogs.length);
      } else {
        console.log('❌ Failed to get featured blogs');
      }

      // Test 6: Get all tags
      console.log('\n6. Getting all tags...');
      const getTagsResponse = await fetch(`${baseUrl}/tags`);
      if (getTagsResponse.ok) {
        const tags = await getTagsResponse.json();
        console.log('✅ Retrieved tags:', tags);
      } else {
        console.log('❌ Failed to get tags');
      }

      console.log('\n🎉 All tests completed successfully!');
      console.log('\n📝 Blog API is working correctly with the following endpoints:');
      console.log('   - POST /api/blog (Create)');
      console.log('   - GET /api/blog (Get all with pagination)');
      console.log('   - GET /api/blog/:id (Get by ID)');
      console.log('   - GET /api/blog/slug/:slug (Get by slug)');
      console.log('   - GET /api/blog/featured (Get featured)');
      console.log('   - GET /api/blog/tags (Get all tags)');

    } else {
      const error = await createResponse.text();
      console.log('❌ Failed to create blog:', error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test if this script is executed directly
if (typeof window === 'undefined') {
  testBlogAPI();
}
