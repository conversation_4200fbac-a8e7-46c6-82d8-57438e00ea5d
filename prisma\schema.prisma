generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearchPostgres"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           Int     @id @default(autoincrement())
  name         String
  email        String  @unique
  password     String
  firstName    String?
  lastName     String?
  companyName  String?
  country      String?
  streetAddress String?
  city         String?
  state        String?
  zipCode      String?
  phone        String?
  points       Int     @default(0)
  isA<PERSON><PERSON> @default(false)
  isSuperAdmin <PERSON> @default(false)
  isVerified   Boolean @default(false)
  isBanned     Boolean @default(false)
  bannedReason String?
  bannedUntil  DateTime?
  bannedBy     User?   @relation("BannedBy", fields: [bannedById], references: [id])
  bannedById   Int?
  bannedUsers  User[]   @relation("BannedBy")
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model Product {
  id                Int                 @id @default(autoincrement())
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  sku               String              @unique
  name              String
  slug              String              @unique
  description       String?
  shortDescription  String?
  price             Decimal             @db.Decimal(10, 2)
  salePrice         Decimal?            @db.Decimal(10, 2)
  saleStart         DateTime?
  saleEnd           DateTime?
  stockQuantity     Int?
  stockStatus       StockStatus         @default(IN_STOCK)
  taxStatus         TaxStatus           @default(TAXABLE)
  taxClass          TaxClass            @default(STANDARD)
  type              ProductType         @default(SIMPLE)
  access            AccessLevel         @default(PUBLIC)
  password          String?
  search_vector     Unsupported("tsvector")?

  ProductCategories ProductCategories[]
  images            ProductImage[]
  ProductTags       ProductTags[]
  variants          ProductVariant[]
  categories        Category[]          @relation("ProductCategories")
  tags              Tag[]               @relation("ProductTags")
  ProductAttribute  ProductAttribute[]
  listings          Listing[]
  ProductSectionItems ProductSectionItem[]

  @@index([search_vector], type: Gin)
  @@index([name(ops: raw("gin_trgm_ops"))], type: Gin)
}

model ProductImage {
  id             Int              @id @default(autoincrement())
  url            String
  position       Int              @default(0)
  productId      Int?
  product        Product?          @relation(fields: [productId], references: [id])
  variantId  Int?
  variant    ProductVariant?  @relation(fields: [variantId], references: [id])
}

model ProductVariant {
  id            Int                     @id @default(autoincrement())
  sku           String                  @unique
  price         Decimal                 @db.Decimal(10, 2)
  salePrice     Decimal?                @db.Decimal(10, 2)
  saleStart     DateTime?
  saleEnd       DateTime?
  stockQuantity Int?
  stockStatus   StockStatus             @default(IN_STOCK)
  productId     Int
  product       Product                 @relation(fields: [productId], references: [id])
  attributes    VariantAttributeValue[]

  ProductImage ProductImage[]
}

model Attribute {
  id     Int              @id @default(autoincrement())
  name   String           @unique
  ProductAttribute ProductAttribute[]
}

// model AttributeValue {
//   id          Int                     @id @default(autoincrement())
//   value       String
//   attributeId Int
//   attribute   Attribute               @relation(fields: [attributeId], references: [id])
//   priceModifier Decimal?             @db.Decimal(10, 2)
//   variants    VariantAttributeValue[]
// }

/// pivot table tying a Product → an Attribute → its set of values
model ProductAttribute {
  id           Int            @id @default(autoincrement())
  product      Product        @relation(fields: [productId], references: [id])
  productId    Int
  attribute    Attribute      @relation(fields: [attributeId], references: [id])
  attributeId  Int

  values           ProductAttributeValue[]
}
model ProductAttributeValue {
  id                   Int                    @id @default(autoincrement())
  productAttribute     ProductAttribute       @relation(fields: [productAttributeId], references: [id])
  productAttributeId   Int

  /// e.g. "M", "L" or "Red", "Yellow"
  value                String

  /// which variants include this specific value
  assignments          VariantAttributeValue[]

}

model VariantAttributeValue {
  variantId Int
  valueId   Int
  value     ProductAttributeValue @relation(fields: [valueId], references: [id])
  variant   ProductVariant @relation(fields: [variantId], references: [id])

  @@id([variantId, valueId])
}

model MainCategory {
  id          Int        @id @default(autoincrement())
  name        String     @unique
  slug        String     @unique
  categories  Category[]
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  imageUrl    String     @default("https://placehold.co/600x400?text=Catgeory")
}

model Category {
  id                Int                 @id @default(autoincrement())
  name              String              @unique
  slug              String              @unique
  description       String?
  mainCategoryId    Int?
  mainCategory      MainCategory?       @relation(fields: [mainCategoryId], references: [id])
  ProductCategories ProductCategories[]
  products          Product[]           @relation("ProductCategories")
  imageUrl          String              @default("https://placehold.co/600x400?text=Catgeory")
}

model Tag {
  id          Int           @id @default(autoincrement())
  name        String        @unique
  slug        String        @unique
  ProductTags ProductTags[]
  products    Product[]     @relation("ProductTags")
}

model ProductCategories {
  productId  Int
  categoryId Int
  category   Category @relation(fields: [categoryId], references: [id])
  product    Product  @relation(fields: [productId], references: [id])

  @@id([productId, categoryId])
}

model ProductTags {
  productId Int
  tagId     Int
  product   Product @relation(fields: [productId], references: [id])
  tag       Tag     @relation(fields: [tagId], references: [id])

  @@id([productId, tagId])
}

model Listing {
  id          Int       @id @default(autoincrement())
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  title       String
  content     String    @db.Text
  productId   Int
  product     Product   @relation(fields: [productId], references: [id])
}

enum StockStatus {
  IN_STOCK
  OUT_OF_STOCK
  ON_BACKORDER
}

enum TaxStatus {
  TAXABLE
  SHIPPING
  NONE
}

enum TaxClass {
  STANDARD
  REDUCED
  ZERO
}

enum ProductType {
  SIMPLE
  VARIABLE
  GROUPED
  EXTERNAL
}

enum AccessLevel {
  PUBLIC
  PRIVATE
  PROTECTED
}

model ProductSection {
  id          Int                  @id @default(autoincrement())
  name        String
  position    Int                  @unique
  createdAt   DateTime             @default(now())
  updatedAt   DateTime             @updatedAt
  items       ProductSectionItem[]
}

model ProductSectionItem {
  id              Int            @id @default(autoincrement())
  productId       Int
  product         Product        @relation(fields: [productId], references: [id])
  productSectionId Int
  productSection  ProductSection @relation(fields: [productSectionId], references: [id])
  position        Int
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  @@unique([productSectionId, position])
}

model TaxSettings {
  id        Int      @id @default(autoincrement())
  name      String   @default("Default Tax")
  value     Decimal  @db.Decimal(10, 2)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Blog {
  id           Int      @id @default(autoincrement())
  title        String
  slug         String   @unique
  tags         String[] // Array of tag strings
  content      Json     // JSON array of content blocks
  blogImage    String?  // Main blog image URL
  author       String?
  authorName   String?  // Author name (fallback if no user)
  authorAvatar String?  // Author avatar URL
  authorBio    String?  // Author bio
  createdDate  DateTime @default(now())
  excerpt      String?  // Short description
  readTime     Int?     // Estimated read time in minutes
  featured     Boolean  @default(false)
  published    Boolean  @default(false)
  metaTitle    String?  // SEO meta title
  metaDescription String? // SEO meta description
  keywords     String[] // SEO keywords array
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

