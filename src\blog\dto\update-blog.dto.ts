import { IsOptional, IsString, IsA<PERSON>y, IsBoolean, IsInt, IsPositive } from 'class-validator';
import { Type } from 'class-transformer';
import { BlogContentBlockDto, BlogAuthorDto, BlogSeoDto } from './create-blog.dto';

export class UpdateBlogDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsArray()
  @Type(() => BlogContentBlockDto)
  content?: BlogContentBlockDto[];

  @IsOptional()
  @IsString()
  blogImage?: string;

  @IsOptional()
  @IsInt()
  @IsPositive()
  authorId?: number;

  @IsOptional()
  @Type(() => BlogAuthorDto)
  author?: BlogAuthorDto;

  @IsOptional()
  @IsString()
  excerpt?: string;

  @IsOptional()
  @IsInt()
  @IsPositive()
  readTime?: number;

  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @IsOptional()
  @IsBoolean()
  published?: boolean;

  @IsOptional()
  @Type(() => BlogSeoDto)
  seo?: BlogSeoDto;
}
